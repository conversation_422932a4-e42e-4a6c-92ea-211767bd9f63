"use client";

import React from 'react';

class ChunkErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Check if it's a chunk loading error
    if (error?.name === 'ChunkLoadError' || 
        error?.message?.includes('Loading chunk') ||
        error?.message?.includes('Loading CSS chunk')) {
      return { hasError: true, error };
    }
    return null;
  }

  componentDidCatch(error, errorInfo) {
    // Log chunk loading errors
    if (error?.name === 'ChunkLoadError' || 
        error?.message?.includes('Loading chunk')) {
      console.error('Chunk loading error:', error, errorInfo);
      
      // Attempt to reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center p-8">
            <div className="mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-default mx-auto"></div>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Đang tải lại trang...
            </h2>
            <p className="text-gray-600 mb-4">
              Có lỗi xảy ra khi tải trang. Chúng tôi đang tự động tải lại.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary-default text-white rounded-lg hover:bg-primary-hover transition-colors"
            >
              Tải lại ngay
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ChunkErrorBoundary;
