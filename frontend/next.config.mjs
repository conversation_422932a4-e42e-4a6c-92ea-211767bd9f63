import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';
const parsedUrl = new URL(strapiBaseUrl);

/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: parsedUrl.protocol.replace(':', ''),
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || '',
                pathname: '/uploads/**',
            },
        ],
        domains: ['localhost','ongbadayhoa.com','be.ongbadayhoa.com'],
    },
    webpack: (config, { dev, isServer }) => {
        config.resolve.alias['@'] = __dirname;

        // Fix chunk loading issues in development
        if (dev && !isServer) {
            config.optimization = {
                ...config.optimization,
                splitChunks: {
                    ...config.optimization.splitChunks,
                    cacheGroups: {
                        ...config.optimization.splitChunks?.cacheGroups,
                        default: {
                            minChunks: 1,
                            priority: -20,
                            reuseExistingChunk: true,
                        },
                        vendor: {
                            test: /[\\/]node_modules[\\/]/,
                            name: 'vendors',
                            priority: -10,
                            chunks: 'all',
                        },
                    },
                },
            };
        }

        return config;
    },
    // serverActions: {
    //     bodySizeLimit: '50mb',
    // },
    reactStrictMode: true,

    // Turbopack configuration (moved from experimental.turbo)
    turbopack: {
        rules: {
            '*.svg': {
                loaders: ['@svgr/webpack'],
                as: '*.js',
            },
        },
    },
};

export default nextConfig;
