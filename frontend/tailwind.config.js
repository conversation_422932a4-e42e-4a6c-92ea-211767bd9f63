/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./app/**/*.{js,ts,jsx,tsx,mdx}",
        "./app/**/**/*.{js,ts,jsx,tsx,mdx}",
        "./pages/**/*.{js,ts,jsx,tsx,mdx}",
        "./components/**/*.{js,ts,jsx,tsx,mdx}",

        // Or if using `src` directory:
        "./src/**/*.{js,ts,jsx,tsx,mdx}",
    ],
    theme: {
        extend: {
            spacing: {
                'none' : '0px',
                'xxs' : '2px',
                'xs' : '4px',
                'sm' : '6px',
                'md' : '8px',
                'lg' : '12px',
                'xl' : '16px',
                '2xl' : '20px',
                '3xl' : '24px',
                '4xl' : '32px',
                '5xl' : '40px',
                '6xl' : '48px',
                '7xl' : '64px',
                '8xl' : '80px',
                '9xl' : '96px',
                '10xl' : '128px',
                '11xl' : '160px',
            },
            fontSize: {
                'xxs': '10px',
                'xs': '12px',
                'sm': '14px',
                'md': '16px',
                'lg': '18px',
                'xl': '20px',
                'display-xs': '24px',
                'display-sm': '30px',
                'display-md': '36px',
                'display-lg': '48px',
                'display-xl': '60px',
                'display-2xl':'72px',
            },
            lineHeight: {
                'xxs': '16px',
                'xs': '18px',
                'sm': '20px',
                'md': '24px',
                'lg': '28px',
                'xl': '30px',
                'display-xs': '32px',
                'display-sm': '38px',
                'display-md': '44px',
                'display-lg': '60px',
                'display-xl': '72px',
                'display-2xl': '90px',
            },
            borderRadius: {
                'none':'0px',
                'xxs':'2px',
                'xs':'4px',
                'sm':'6px',
                'md':'8px',
                'lg':'10px',
                'xl':'12px',
                '2xl':'16px',
                '3xl':'20px',
                '4xl':'24px',
                'full':'9999px',
            },
            boxShadow: {
                'xs': '0px 1px 2px rgba(10, 13, 14, 0.06), 0px 1px 8px rgba(10, 13, 14, 0.04)',
            }
        },
        backgroundColor: {
            'primary': {
                'default': '#299D55',
                'hover': '#198C43',
                'pressed': '#198C43',
                'focused': '#299D55',
                'disabled': '#F5F5F5'
            },
            'secondary-gray': {
                'default': '#FFFFFF',
                'hover': '#FAFAFA',
                'pressed': '#FAFAFA',
                'focused': '#FFFFFF',
                'disabled': '#FFFFFF'
            },
            'secondary-color': {
                'default': '#FFFFFF',
                'hover': '#F0FFF7',
                'pressed': '#F0FFF7',
                'focused': '#FFFFFF',
                'disabled': '#FFFFFF'
            },
            'utility-brand-50': '#F0FFF7',
            'white': '#FFFFFF',
            'utility-blue-600': '#1570EF',
            'utility-blue-50': '#EFF8FF',
            'brand-solid': '#299D55',
            'utility-success-50' : '#ECFDF3',
            'utility-warning-50' : '#FFFAEB'
        },
        backgroundImage: {
            'brand-gradient': 'linear-gradient(89deg, #f0fff7 1.35%, #b5f2d7 100.76%)',
        },
        textColor: {
            'primary': {
                'default': '#FFFFFF',
                'hover': '#FFFFFF',
                'pressed': '#FFFFFF',
                'focused': '#FFFFFF',
                'disabled': '#A4A7AE'
            },
            'secondary-gray': {
                'default': '#414651',
                'hover': '#252B37',
                'pressed': '#252B37',
                'focused': '#414651',
                'disabled': '#A4A7AE'
            },
            'secondary-color': {
                'default': '#198C43',
                'hover': '#146630',
                'pressed': '#146630',
                'focused': '#198C43',
                'disabled': '#A4A7AE'
            },
            'placeholder': '#717680',
            'utility-gray-400': '#A4A7AE',
            'secondary-700': '#414651',
            'primary-900': '#181D27',
            'error-primary-600': '#D92D20',
            'white': '#FFFFFF',
            'utility-success-700': '#067647',
            'utility-warning-700' : '#B54708',
            'brand-secondary': '#198C43'

        },
        borderColor: {
            'primary': {
                'default': '#299D55',
                'hover': '#198C43',
                'pressed': '#198C43',
                'focused': '#299D55',
                'disabled': '#E9EAEB'
            },
            'secondary-gray': {
                'default': '#D5D7DA',
                'hover': '#D5D7DA',
                'pressed': '#D5D7DA',
                'focused': '#D5D7DA',
                'disabled': '#E9EAEB'
            },
            'secondary-color': {
                'default': '#8EE5BA',
                'hover': '#8EE5BA',
                'pressed': '#8EE5BA',
                'focused': '#8EE5BA',
                'disabled': '#E9EAEB'
            },
            'utility-brand-200':'#B5F2D7',
            'secondary' : '#E9EAEB',
            'skeuemorphic-gradient-border': 'rgba(255, 255, 255, 0.12)',
            'utility-success-200': '#ABEFC6',
            'utility-warning-200' : '#FEDF89'
        },
        screens: {
            xs: '325px',
            sm: '641px',
            md: '961px',
            lg: '1024px',
            'custom': '1142px',
            'custom2': '1166px',
            'custom3': '1280px',
            'custom4': '1055px',
            xl: '1441px',


        },
        colors: {
            'Colors-Text-text-disabled': '#717680',
            'quaternary-500': '#181D27',
            'white': '#ffffff'
        },
        fontFamily: {
            'inter': ['Inter', 'sans-serif'], // Thêm font Inter
        },

    },
    plugins: [
        require('@tailwindcss/typography'),
        // require('@tailwindcss/line-clamp'), // Removed - now included by default in Tailwind CSS v3.3+
    ],
}

