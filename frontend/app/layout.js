// frontend/app/layout.js
// Đã chuyển thành Server Component (bỏ "use client")

import "../styles/globals.css";
import { Inter } from "next/font/google";
import AppShell from "../components/AppShell"; // Import AppShell
import GoogleAnalytics from "../components/GoogleAnalytics"; // Thêm import này
import ChunkErrorBoundary from "../components/ChunkErrorBoundary"; // Import Error Boundary
import Script from 'next/script';

const inter = Inter({
  subsets: ["latin"],
  weight: ["400", "700"],
  display: "swap",
  variable: "--font-inter", // Thêm variable để có thể dùng trong CSS nếu cần
});

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

export const metadata = {
  metadataBase: new URL(siteUrl),
  title: {
    default: 'Ông Ba Dạy Hóa - Học <PERSON>a Online Chất Lượng Cao', // Title mặc định
    template: '%s | Ông Ba Dạy Hóa', // Template cho các trang con
  },
  description: 'Khóa học Hóa online THPT với phương pháp trực quan, thí nghiệm thực tế, lộ trình cá nhân hóa cùng Ông Ba Dạy Hóa. Chinh phục điểm cao môn Hóa ngay!',
  applicationName: 'Ông Ba Dạy Hóa',
  authors: [{ name: 'Ông Ba Dạy Hóa', url: siteUrl }],
  keywords: ['học hóa online', 'ông ba dạy hóa', 'luyện thi hóa', 'hóa học 10', 'hóa học 11', 'hóa học 12', 'hóa thpt'],
  creator: 'Ông Ba Dạy Hóa Team',
  publisher: 'Ông Ba Dạy Hóa',
  referrer: 'origin-when-cross-origin',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: '/Favi.png', // Đường dẫn đến favicon trong public
    // shortcut: '/shortcut-icon.png',
    // apple: '/apple-touch-icon.png',
    // other: {
    //   rel: 'apple-touch-icon-precomposed',
    //   url: '/apple-touch-icon-precomposed.png',
    // },
  },
  manifest: '/site.webmanifest',
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION, // Thêm NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION vào .env
    // yandex: 'YOUR_YANDEX_VERIFICATION_CODE',
    // other: {
    //   me: ['<EMAIL>', 'https://your-link.com'],
    // },
  },
  // Open Graph metadata mặc định (sẽ được override bởi metadata của trang cụ thể)
  openGraph: {
    type: 'website',
    locale: 'vi_VN',
    url: siteUrl,
    siteName: 'Ông Ba Dạy Hóa',
    title: 'Ông Ba Dạy Hóa - Học Hóa Online Chất Lượng Cao',
    description: 'Khóa học Hóa online THPT với phương pháp trực quan, thí nghiệm thực tế...',
    images: [
      {
        url: `${siteUrl}/images/metadata-img/homepage-og.jpg`, // Ảnh OG mặc định
        width: 1200,
        height: 630,
        alt: 'Ông Ba Dạy Hóa',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    // site: '@YourTwitterHandle', // Twitter handle của website
    // creator: '@CreatorTwitterHandle', // Twitter handle của người tạo nội dung
    title: 'Ông Ba Dạy Hóa - Học Hóa Online Chất Lượng Cao',
    description: 'Khóa học Hóa online THPT với phương pháp trực quan...',
    images: [`${siteUrl}/images/metadata-img/homepage-og.jpg`], // Ảnh Twitter mặc định
  },
  // viewport: 'width=device-width, initial-scale=1', // Next.js tự động xử lý viewport
  // themeColor: '#198C43', // Màu chủ đề cho trình duyệt trên mobile
};

export default function RootLayout({ children }) {
  return (
    <html lang="vi" className={inter.variable}>
      {/* Thẻ <head> sẽ được Next.js tự động quản lý với metadata object */}
      <body className={`${inter.className} antialiased`}>
        {/* Hotjar Tracking Code */}
        {process.env.NEXT_PUBLIC_HOTJAR_ID && (
          <>
            <Script
              id="hotjar-tracking"
              strategy="afterInteractive"
              dangerouslySetInnerHTML={{
                __html: `
                  (function(h,o,t,j,a,r){
                    h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                    h._hjSettings={hjid:${process.env.NEXT_PUBLIC_HOTJAR_ID},hjsv:6};
                    a=o.getElementsByTagName('head')[0];
                    r=o.createElement('script');r.async=1;
                    r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                    a.appendChild(r);
                  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
                `,
              }}
            />
          </>
        )}
        <GoogleAnalytics />
        <ChunkErrorBoundary>
          <AppShell>{children}</AppShell>
        </ChunkErrorBoundary>
      </body>
    </html>
  );
}
